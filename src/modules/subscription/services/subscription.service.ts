import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import { deepCasting } from 'src/internal/casting/object.casting';
import { ModuleTypeEnum } from 'src/modules/bkash/entities/bkash.entity';
import { BkashInternalProvider } from 'src/modules/bkash/providers/internal.provider';
import { CheckoutDto, CheckoutResponseDto, PlanDto, PlansResponseDto, SubscriptionDto, SubscriptionsResponseDto } from '../dtos/subscription.dto';
import {
  AccessTypeEnum,
  FeatureAccessEntity,
  FeatureAccessStatusDto,
  SubscriptionCheckoutDto,
  SubscriptionEntity,
  SubscriptionStatusEnum
} from '../entities/subscription.entity';
import { FeatureTypeEnum } from '../plans/common/entities/plans.entity';
import { UserPlanRepository } from '../plans/repositories/user.repository';
import { FeatureAccessRepository } from '../repositories/feature-access.repository';
import { SubscriptionRepository } from '../repositories/subscription.repository';

@Injectable()
export class SubscriptionService {
  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly featureAccessRepository: FeatureAccessRepository,
    private readonly planRepository: UserPlanRepository,
    private readonly bkashProvider: BkashInternalProvider,
    private readonly helper: Helper,
  ) {}

  /**
   * Check if user has access to a specific feature
   *
   * Source of truth: FeatureAccess entries (Paid or Trial)
   */
  async checkFeatureAccess(userId: string, featureType: FeatureTypeEnum): Promise<FeatureAccessStatusDto> {
    const now = new Date();

    // 1) Check active paid feature access
    const activePaidAccess = await this.featureAccessRepository.findActivePaidAccess(userId, featureType);
    if (activePaidAccess) {
      return {
        featureType,
        hasAccess: true,
        accessType: AccessTypeEnum.Paid,
        isTrialActive: false,
        subscriptionEndsAt: activePaidAccess.endDate,
        usageCount: activePaidAccess?.usageCount || 0,
        usageLimit: activePaidAccess?.usageLimit,
        remainingUsage: activePaidAccess?.usageLimit ? (activePaidAccess.usageLimit - activePaidAccess.usageCount) : undefined,
      };
    }

    // 2) No active paid access: if there is any paid access that expired, return expired state (to inform UI and guard)
    const latestPaidAccess = await this.featureAccessRepository.findLatestPaidAccess(userId, featureType);
    if (latestPaidAccess && latestPaidAccess.endDate && latestPaidAccess.endDate <= now) {
      return {
        featureType,
        hasAccess: false,
        accessType: AccessTypeEnum.Paid,
        isTrialActive: false,
        subscriptionEndsAt: latestPaidAccess.endDate,
        usageCount: 0,
      };
    }

    // Check for trial access
    const trialAccess = await this.featureAccessRepository.findActiveTrialAccess(userId, featureType);
    if (trialAccess && trialAccess.endDate && trialAccess.endDate > now) {
      const hasUsageLeft = !trialAccess.usageLimit || trialAccess.usageCount < trialAccess.usageLimit;
      
      return {
        featureType,
        hasAccess: hasUsageLeft,
        accessType: AccessTypeEnum.Trial,
        isTrialActive: true,
        trialEndsAt: trialAccess.endDate,
        usageCount: trialAccess.usageCount,
        usageLimit: trialAccess.usageLimit,
        remainingUsage: trialAccess.usageLimit ? (trialAccess.usageLimit - trialAccess.usageCount) : undefined,
      };
    }

    // 3) No access found
    return {
      featureType,
      hasAccess: false,
      accessType: AccessTypeEnum.Trial,
      isTrialActive: false,
      usageCount: 0,
    };
  }

  /**
   * Start trial access for a feature
   */
  async startTrial(userId: string, featureType: FeatureTypeEnum): Promise<FeatureAccessEntity> {
    // Check if user already had trial for this feature
    const existingTrialAccess = await this.featureAccessRepository.findTrialAccess(userId, featureType);
    if (existingTrialAccess) {
      throw new BadRequestException(`Trial for ${featureType} has already been used`);
    }

    // Get feature configuration (this would be from database in real implementation)
    const featureConfig = await this.planRepository.getFeatureByFeatureType(featureType);
    if (!featureConfig) {
      throw new NotFoundException(`Configuration for Feature ${featureType} not found`);
    }
    const trialStartDate = new Date();
    const trialEndDate = new Date(trialStartDate.getTime() + featureConfig.trialDurationDays * 24 * 60 * 60 * 1000);
    const trialAccess: FeatureAccessEntity = {
      userId,
      featureType,
      accessType: AccessTypeEnum.Trial,
      startDate: trialStartDate,
      endDate: trialEndDate,
      usageCount: 0,
      usageLimit: featureConfig.trialUsageLimit,
      isActive: true,
    };

    return await this.featureAccessRepository.create(trialAccess);
  }

  /**
   * Record feature usage and check access - automatically starts trial if eligible
   */
  async recordUsageAndCheckAccess(userId: string, featureType: FeatureTypeEnum): Promise<boolean> {
    const accessStatus = await this.checkFeatureAccess(userId, featureType);
    if (!accessStatus.hasAccess) {
      // Check if user is eligible for trial (hasn't used trial before)
      const existingTrialAccess = await this.featureAccessRepository.findTrialAccess(userId, featureType);
      
      if (!existingTrialAccess && !accessStatus.isTrialActive && !accessStatus.subscriptionEndsAt) {
        // User is eligible for trial - start it automatically
        try {
          await this.startTrial(userId, featureType);
          // After starting trial, increment usage and return true
          await this.featureAccessRepository.incrementUsage(userId, featureType);
          return true;
        } catch (error) {
          // If trial creation fails, return false
          return false;
        }
      }
      
      // User is not eligible for trial or trial creation failed
      return false;
    }

    // User has access - increment usage count
    await this.featureAccessRepository.incrementUsage(userId, featureType);
    
    return true;
  }

  /**
   * Create subscription checkout for bKash payment
   */
  async createSubscriptionCheckout(userId: string, checkoutDto: SubscriptionCheckoutDto): Promise<CheckoutResponseDto> {
    const plan = await this.planRepository.getPlanById(checkoutDto.planId);
    if (!plan) {
      throw new NotFoundException(`Plan ${checkoutDto.planId} not found`);
    }

    // Check if user already has an active subscription
    const existingSubscription = await this.subscriptionRepository.findActiveSubscription(userId);
    
    let subscriptionToUse: SubscriptionEntity;
    
    if (existingSubscription) {
      // Check if trying to subscribe to the same plan
      if (existingSubscription.planId === checkoutDto.planId) {
        throw new BadRequestException(`You currently have an active subscription for this plan. Your subscription is valid until ${existingSubscription.endDate.toISOString()}. If you want to upgrade to a different plan, please select a different plan.`);
      }
      
      // Upgrade: Update existing subscription with new plan details
      subscriptionToUse = await this.subscriptionRepository.updateSubscriptionPlan(
        existingSubscription.id,
        plan.id,
        plan.planType,
        new Date(Date.now() + plan.durationDays * 24 * 60 * 60 * 1000)
      );
    } else {
      // Create new subscription
      const subscription: SubscriptionEntity = {
        userId,
        planId: plan.id,
        planType: plan.planType,
        status: SubscriptionStatusEnum.PendingPayment,
        startDate: new Date(),
        endDate: new Date(Date.now() + plan.durationDays * 24 * 60 * 60 * 1000),
        autoRenew: checkoutDto.autoRenew || false,
      };
      
      subscriptionToUse = await this.subscriptionRepository.create(subscription);
    }

    if (!subscriptionToUse.id) {
      throw new BadRequestException('Failed to process subscription');
    }

    // Create bKash payment
    const bkashPayment = await this.bkashProvider.createBkashPayment({
      userId,
      referenceId: subscriptionToUse.id,
      amount: plan.price,
      moduleType: ModuleTypeEnum.Subscription,
      isSavePayment: false,
    });

    if (!bkashPayment.paymentURL) {
      throw new BadRequestException('Failed to create bKash payment');
    }

    // Return payment URL
    const response = { paymentURL: bkashPayment.paymentURL };
    const responseDto = deepCasting(CheckoutDto, response);
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  /**
   * Activate subscription after successful payment
   */
  async activateSubscription(subscriptionId: string): Promise<void> {
    const subscription = await this.subscriptionRepository.findById(subscriptionId);
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    const plan = await this.planRepository.getPlanById(subscription.planId);
    if (!plan) {
      throw new NotFoundException('Plan not found');
    }

    // Update subscription status to Active
    await this.subscriptionRepository.updateStatus(subscriptionId, SubscriptionStatusEnum.Active);

    // Create or update feature access records for all features in the plan
    for (const feature of plan.features) {
      const featureAccess: FeatureAccessEntity = {
        userId: subscription.userId,
        featureType: feature.featureType,
        accessType: AccessTypeEnum.Paid,
        startDate: subscription.startDate,
        endDate: subscription.endDate,
        usageCount: 0,
        subscriptionId: subscriptionId,
        isActive: true,
      };

      // Deactivate trial access
      await this.featureAccessRepository.deactivateTrialAccess(subscription.userId, feature.featureType);
      
      // Create or update paid access
      await this.featureAccessRepository.createOrUpdate(featureAccess);
    }
  }

  /**
   * Mark subscription as failed after an unsuccessful payment
   */
  async failedSubscription(subscriptionId: string): Promise<void> {
    const subscription = await this.subscriptionRepository.findById(subscriptionId);
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }
    // Move to Cancelled state; no active access should exist, but deactivate if any
    await this.subscriptionRepository.updateStatus(subscriptionId, SubscriptionStatusEnum.Cancelled);
    await this.featureAccessRepository.deactivateBySubscription(subscriptionId);
  }

  /**
   * Get user's active subscriptions
   */
  async getUserSubscriptions(userId: string): Promise<SubscriptionsResponseDto> {
    const result = await this.subscriptionRepository.findUserSubscriptions(userId);
    const responseDto = result.map((item) => deepCasting(SubscriptionDto, item));
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(subscriptionId: string, userId?: string): Promise<void> {
    const subscription = await this.subscriptionRepository.findById(subscriptionId);
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }
    if (userId && subscription.userId !== userId) {
      throw new NotFoundException('Subscription not found');
    }
    await this.subscriptionRepository.updateStatus(subscriptionId, SubscriptionStatusEnum.Cancelled);

    await this.featureAccessRepository.deactivateBySubscription(subscriptionId);
  }


  /**
   * Get all available subscription plans
   */
  async getPlans(): Promise<PlansResponseDto> {
    const plans = await this.planRepository.findAllActivePlans();
    const responseDto = plans.map((item) => deepCasting(PlanDto, item));
    return this.helper.serviceResponse.successResponse(responseDto);
  }
}

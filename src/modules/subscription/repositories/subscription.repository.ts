import { Injectable } from '@nestjs/common';
import { SubscriptionModel } from 'src/database/subscription/subscription.model';
import { SubscriptionEntity, SubscriptionStatusEnum } from '../entities/subscription.entity';

@Injectable()
export class SubscriptionRepository {
  
  async create(subscription: SubscriptionEntity): Promise<SubscriptionEntity> {
    const createdSubscription = new SubscriptionModel(subscription);
    await createdSubscription.save();
    return createdSubscription.toObject();
  }

  async findById(id: string): Promise<SubscriptionEntity | null> {
    const subscription = await SubscriptionModel.findOne({ id }).lean();
    return subscription;
  }

  async findActiveSubscription(userId: string): Promise<SubscriptionEntity | null> {
    const now = new Date();
    const subscription = await SubscriptionModel.findOne({
      userId,
      status: SubscriptionStatusEnum.Active,
      endDate: { $gt: now },
    }).lean();
    return subscription;
  }

  async findUserSubscriptions(userId: string): Promise<SubscriptionEntity[]> {
    const subscriptions = await SubscriptionModel.find({ userId }).lean();
    return subscriptions;
  }

  async updateStatus(subscriptionId: string, status: SubscriptionStatusEnum): Promise<SubscriptionEntity> {
    const updatedDoc = await SubscriptionModel.findOneAndUpdate(
      { id: subscriptionId },
      { $set: { status } },
      { new: true, runValidators: true },
    ).exec();
    return updatedDoc !== null ? updatedDoc.toObject() : null;
  }

  async updateSubscriptionPlan(subscriptionId: string, planId: string, planType: string, endDate: Date): Promise<SubscriptionEntity> {
    const updatedDoc = await SubscriptionModel.findOneAndUpdate(
      { id: subscriptionId },
      { 
        $set: { 
          planId,
          planType,
          endDate,
          status: SubscriptionStatusEnum.PendingPayment, // Reset to pending for new payment
          updatedAt: new Date()
        } 
      },
      { new: true, runValidators: true },
    ).exec();
    return updatedDoc !== null ? updatedDoc.toObject() : null;
  }
}

import { Injectable, Module, OnApplicationBootstrap } from '@nestjs/common';
import { QueueInstance } from 'src/queue-system';
import { NotificationQueueName } from 'src/queue-system/predefined-data';
import { NotificationRequestEvent } from 'src/queue-system/types';
import { InsertNotification } from 'src/queue-system/database/notification';
import { NotificationGenerator } from 'src/queue-system/notification/generator';

@Injectable()
export class DailyReminderGeneratorQueue implements OnApplicationBootstrap {
  private static readonly REMINDER_TOPIC = 'fitsomnia-reminders';

  async onApplicationBootstrap() {
    try {
      await (
        await QueueInstance
      ).consume(NotificationQueueName.DAILY_REMINDER_GENERATOR_QUEUE);
    } catch (error) {
      console.log(error.message);
    }
  }

  static async generator(
    requestEvent: NotificationRequestEvent,
  ): Promise<void> {
    try {
      // Handle reminder notifications directly without calling CommonNotificationGeneratorQueue
      // to prevent duplicate notifications
      const { targetUsers, title, content, module, type, documentId, saveDatabase, data, topic, body } = requestEvent;

      // Determine current environment for topic isolation
      const currentEnv = (process.env.ENV || 'LOCAL').toLowerCase();
      const currentEnvTopic = `${this.REMINDER_TOPIC}-${currentEnv}`;

      // Save notifications to database for in-app notifications if required
      if (saveDatabase && targetUsers?.length) {
        // Create a proper request event with createdBy for reminders
        const reminderRequestEvent = {
          ...requestEvent,
          createdBy: requestEvent.createdBy || {
            userId: 'system',
            name: 'Fitsomnia',
            avatar: null,
          },
        };

        const notification = NotificationGenerator.generateNotification(
          reminderRequestEvent,
          type,
        );

        notification.title = title;
        notification.content = content;

        // Generate notification recipients
        const notificationRecipients = NotificationGenerator.generateNotificationRecipients(
          targetUsers,
          notification?.id,
          module,
        );

        // Save the notification and notificationRecipients in the database
        await InsertNotification(notification, notificationRecipients);
      }

      // Send topic notification for FCM push notifications (this is the main reminder notification)
      if (topic && title && body) {
        // Create payload for topic sender with environment-specific topic
        const topicPayload = {
          title,
          body,
          topic: currentEnvTopic, // Use environment-specific topic for isolation
          isHighPriority: true,
          task: 'topic-sender',
          payloadType: 'DAILY_REMINDER_SENDER',
          data,
          documentId,
        };

        // Send to topic sender queue for FCM topic messaging
        const queueInstance = await QueueInstance;
        if (queueInstance) {
          await queueInstance.sendPayload(
            NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE,
            Buffer.from(JSON.stringify(topicPayload)),
          );
        }
      }
    } catch (error: any) {
      console.error('Daily Reminder Generator Queue error:', error.message);
    }
  }
}

@Module({
  providers: [DailyReminderGeneratorQueue],
})
export class DailyReminderGeneratorModule {}

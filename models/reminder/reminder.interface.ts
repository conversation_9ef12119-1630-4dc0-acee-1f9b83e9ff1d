export enum FeatureType {
  REWARDS = 'rewards',
  REFERRALS = 'referrals',
  DAILY_TASKS = 'daily-tasks',
  CHALLENGES = 'challenges',
  DIET = 'diet',
  LEADERBOARD = 'leaderboard',
  OTHER = 'other',
}

export interface IReminder {
  id: string;
  userId: string; // Admin ID who created the reminder
  title: string;
  message: string;
  time: string; // Time in 24-hour format (HH:MM)
  feature: FeatureType;
  isActive: boolean;
  isRepeating: boolean;
  repeatDays: string[]; // Array of days ['monday', 'tuesday', etc.]
  createdAt: Date;
  updatedAt: Date;
}

export interface ICreateReminderRequest {
  title: string;
  message: string;
  time: string;
  feature: FeatureType;
  isActive?: boolean;
  isRepeating?: boolean;
  repeatDays?: string[];
}

export interface IUpdateReminderRequest {
  title?: string;
  message?: string;
  time?: string;
  feature?: FeatureType;
  isActive?: boolean;
  isRepeating?: boolean;
  repeatDays?: string[];
}
